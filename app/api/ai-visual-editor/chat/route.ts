import { openai } from '@ai-sdk/openai'
import { streamText, tool } from 'ai'
import { z } from 'zod'
import { generateObject } from 'ai'
import { analyzeComponentStructure, generateComponentId, validateComponentCode, sanitizeComponentCode } from '@/lib/ai-visual-editor/utils/component-analyzer'
import { generatePropertiesSchema, generateDefaultValues } from '@/lib/ai-visual-editor/utils/properties-generator'

export const maxDuration = 30

const componentSchema = z.object({
  name: z.string(),
  description: z.string(),
  category: z.enum(['layout', 'content', 'media', 'form', 'navigation', 'data']),
  jsx: z.string(),
  imports: z.array(z.string()).optional(),
  dependencies: z.array(z.string()).optional()
})

export async function POST(req: Request) {
  try {
    const { messages } = await req.json()

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      maxSteps: 5,
      tools: {
        generateComponent: tool({
          description: 'Generate a React component with TypeScript and Tailwind CSS',
          parameters: z.object({
            description: z.string().describe('Description of the component to generate'),
            componentType: z.enum(['hero', 'card', 'button', 'form', 'navigation', 'footer', 'custom']),
            features: z.array(z.string()).describe('Specific features or functionality needed'),
            styling: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).default('modern')
          }),
          execute: async ({ description, componentType, features, styling }) => {
            try {
              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: componentSchema,
                prompt: `
                  Generate a React component with the following specifications:
                  - Description: ${description}
                  - Type: ${componentType}
                  - Features: ${features.join(', ')}
                  - Styling: ${styling}
                  
                  Requirements:
                  1. Create a functional React component using TypeScript
                  2. Use Tailwind CSS for styling with ${styling} design approach
                  3. Use shadcn/ui components when appropriate
                  4. Make it responsive and accessible
                  5. Include proper TypeScript interfaces for props
                  6. Use semantic HTML elements
                  7. Add hover states and transitions where appropriate
                  
                  Component should be self-contained and production-ready.
                  Return only the component code without additional explanations.
                  
                  Example structure:
                  \`\`\`tsx
                  interface ComponentNameProps {
                    title?: string
                    description?: string
                    // other props
                  }
                  
                  export function ComponentName({ title, description, ...props }: ComponentNameProps) {
                    return (
                      <div className="...">
                        {/* component content */}
                      </div>
                    )
                  }
                  \`\`\`
                `
              })

              // Validate and sanitize the generated code
              const validation = validateComponentCode(object.jsx)
              if (!validation.isValid) {
                throw new Error(`Generated code validation failed: ${validation.errors.join(', ')}`)
              }

              const sanitizedCode = sanitizeComponentCode(object.jsx)
              
              // Analyze the component structure
              const analysis = analyzeComponentStructure(sanitizedCode)
              
              // Generate properties configuration
              const propertiesConfig = generatePropertiesSchema(analysis)
              const defaultValues = generateDefaultValues(propertiesConfig)
              
              // Create component object
              const component = {
                id: generateComponentId(),
                name: object.name,
                description: object.description,
                category: object.category,
                jsx: sanitizedCode,
                props: {},
                propertiesConfig,
                defaultValues,
                createdAt: new Date(),
                updatedAt: new Date()
              }

              return {
                success: true,
                component,
                message: `Generated ${object.name} component with ${Object.keys(propertiesConfig).length} property sections`
              }
            } catch (error) {
              console.error('Component generation error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to generate component',
                message: 'Component generation failed. Please try again with a different description.'
              }
            }
          }
        }),

        analyzeComponent: tool({
          description: 'Analyze an existing component and generate properties configuration',
          parameters: z.object({
            componentCode: z.string().describe('React component code to analyze'),
            componentName: z.string().describe('Name of the component'),
            focusArea: z.enum(['appearance', 'content', 'behavior', 'data', 'all']).default('all')
          }),
          execute: async ({ componentCode, componentName, focusArea }) => {
            try {
              const analysis = analyzeComponentStructure(componentCode)
              const propertiesConfig = generatePropertiesSchema(analysis)
              const defaultValues = generateDefaultValues(propertiesConfig)

              return {
                success: true,
                componentName,
                propertiesConfig,
                defaultValues,
                analysis: analysis.summary,
                message: `Analyzed ${componentName} and generated ${Object.keys(propertiesConfig).length} property sections`
              }
            } catch (error) {
              console.error('Component analysis error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze component',
                message: 'Component analysis failed. Please check the component code.'
              }
            }
          }
        }),

        optimizeComponent: tool({
          description: 'Optimize an existing component for performance, accessibility, or styling',
          parameters: z.object({
            componentCode: z.string().describe('Current component code'),
            optimizationType: z.enum(['performance', 'accessibility', 'responsive', 'styling']),
            specificRequirements: z.string().optional().describe('Specific optimization requirements')
          }),
          execute: async ({ componentCode, optimizationType, specificRequirements }) => {
            try {
              const { object } = await generateObject({
                model: openai('gpt-4o'),
                schema: z.object({
                  optimizedCode: z.string(),
                  changes: z.array(z.string()),
                  improvements: z.string()
                }),
                prompt: `
                  Optimize the following React component for ${optimizationType}:
                  
                  ${componentCode}
                  
                  ${specificRequirements ? `Specific requirements: ${specificRequirements}` : ''}
                  
                  Focus on:
                  ${optimizationType === 'performance' ? '- Memoization, lazy loading, efficient rendering' : ''}
                  ${optimizationType === 'accessibility' ? '- ARIA labels, keyboard navigation, screen reader support' : ''}
                  ${optimizationType === 'responsive' ? '- Mobile-first design, flexible layouts, breakpoints' : ''}
                  ${optimizationType === 'styling' ? '- Better visual hierarchy, spacing, colors, typography' : ''}
                  
                  Return the optimized code and list the changes made.
                `
              })

              const validation = validateComponentCode(object.optimizedCode)
              if (!validation.isValid) {
                throw new Error(`Optimized code validation failed: ${validation.errors.join(', ')}`)
              }

              return {
                success: true,
                optimizedCode: sanitizeComponentCode(object.optimizedCode),
                changes: object.changes,
                improvements: object.improvements,
                message: `Optimized component for ${optimizationType} with ${object.changes.length} improvements`
              }
            } catch (error) {
              console.error('Component optimization error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to optimize component',
                message: 'Component optimization failed. Please try again.'
              }
            }
          }
        })
      }
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('API route error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: 'Failed to process request'
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
}
