'use client'

import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { 
  Settings, 
  Palette, 
  Type, 
  Zap, 
  Database, 
  Layout,
  RotateCcw,
  Eye,
  EyeOff
} from 'lucide-react'
import { useSelectedComponent, useSelectedComponentProperties, useEditorStore } from '../stores/editor-store'
import { CustomFieldRenderer } from '@/lib/core/builders/components/properties-panel/custom-fields/field-renderer'
import { FieldConfig } from '@/lib/core/builders/components/properties-panel/custom-fields/types'

const tabConfig = [
  {
    id: 'appearance',
    label: 'Appearance',
    icon: Palette,
    description: 'Colors, spacing, borders, shadows'
  },
  {
    id: 'content',
    label: 'Content',
    icon: Type,
    description: 'Text, images, links, media'
  },
  {
    id: 'behavior',
    label: 'Behavior',
    icon: Zap,
    description: 'Animations, interactions, states'
  },
  {
    id: 'data',
    label: 'Data',
    icon: Database,
    description: 'Data sources, API connections'
  },
  {
    id: 'layout',
    label: 'Layout',
    icon: Layout,
    description: 'Positioning, sizing, alignment'
  }
]

export function PropertiesPanel() {
  const selectedComponent = useSelectedComponent()
  const propertyValues = useSelectedComponentProperties()
  const { updatePropertyValue, resetPropertyValues } = useEditorStore()

  if (!selectedComponent) {
    return (
      <div className="h-full flex flex-col bg-white border-r border-gray-200">
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-gray-400" />
            <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          </div>
        </div>
        
        <div className="flex-1 flex items-center justify-center text-gray-500">
          <div className="text-center">
            <EyeOff className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <div className="text-lg font-medium mb-2">No Component Selected</div>
            <div className="text-sm">
              Select a component from the preview or generate one with AI to start editing
            </div>
          </div>
        </div>
      </div>
    )
  }

  const handleFieldChange = (fieldId: string, value: any) => {
    updatePropertyValue(selectedComponent.id, fieldId, value)
  }

  const handleReset = () => {
    resetPropertyValues(selectedComponent.id)
  }

  const renderFieldSection = (sectionId: string, fields: FieldConfig[]) => {
    if (!fields || fields.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <div className="text-sm">No {sectionId} properties available</div>
        </div>
      )
    }

    return (
      <div className="space-y-4 p-4">
        {fields.map((field) => (
          <div key={field.id} className="space-y-2">
            <CustomFieldRenderer
              config={field}
              value={propertyValues[field.id]}
              onChange={(value) => handleFieldChange(field.id, value)}
              onValidate={(isValid, message) => {
                // Handle validation feedback if needed
                if (!isValid && message) {
                  console.warn(`Validation error for ${field.id}:`, message)
                }
              }}
              className="w-full"
            />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="text-xs"
          >
            <RotateCcw className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>
        
        {/* Component Info */}
        <div className="mt-3">
          <div className="flex items-center space-x-2">
            <Eye className="w-4 h-4 text-gray-500" />
            <span className="font-medium text-gray-900">{selectedComponent.name}</span>
            <Badge variant="secondary" className="text-xs">
              {selectedComponent.category}
            </Badge>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            {selectedComponent.description}
          </p>
        </div>
      </div>

      {/* Properties Tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs defaultValue="appearance" className="h-full flex flex-col">
          <div className="border-b border-gray-200 bg-gray-50">
            <TabsList className="grid w-full grid-cols-5 h-auto p-1">
              {tabConfig.map((tab) => {
                const Icon = tab.icon
                const fieldCount = selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig]?.length || 0
                
                return (
                  <TabsTrigger
                    key={tab.id}
                    value={tab.id}
                    className="flex flex-col items-center p-2 text-xs data-[state=active]:bg-white"
                  >
                    <Icon className="w-4 h-4 mb-1" />
                    <span className="font-medium">{tab.label}</span>
                    {fieldCount > 0 && (
                      <Badge variant="secondary" className="text-[10px] px-1 py-0 mt-1">
                        {fieldCount}
                      </Badge>
                    )}
                  </TabsTrigger>
                )
              })}
            </TabsList>
          </div>

          <div className="flex-1 overflow-hidden">
            {tabConfig.map((tab) => (
              <TabsContent
                key={tab.id}
                value={tab.id}
                className="h-full mt-0 data-[state=active]:flex data-[state=active]:flex-col"
              >
                <div className="p-3 border-b border-gray-100 bg-gray-50">
                  <div className="flex items-center space-x-2">
                    <tab.icon className="w-4 h-4 text-gray-600" />
                    <span className="font-medium text-gray-900">{tab.label}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">{tab.description}</p>
                </div>
                
                <ScrollArea className="flex-1">
                  {renderFieldSection(
                    tab.id,
                    selectedComponent.propertiesConfig[tab.id as keyof typeof selectedComponent.propertiesConfig] || []
                  )}
                </ScrollArea>
              </TabsContent>
            ))}
          </div>
        </Tabs>
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          {Object.values(selectedComponent.propertiesConfig).flat().length} properties available
        </div>
      </div>
    </div>
  )
}
