'use client'

import { useMemo, useCallback } from 'react'
import { GeneratedComponent } from '../types'

interface DynamicComponentRendererProps {
  component: GeneratedComponent
  properties: Record<string, any>
  onError?: (error: string) => void
}

export function DynamicComponentRenderer({ 
  component, 
  properties, 
  onError 
}: DynamicComponentRendererProps) {
  
  const renderedComponent = useMemo(() => {
    try {
      // Apply properties to the component JSX
      const processedJSX = applyPropertiesToJSX(component.jsx, properties)
      
      // Create a safe component renderer
      return createSafeComponent(processedJSX, component.name)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown rendering error'
      onError?.(errorMessage)
      return null
    }
  }, [component.jsx, properties, component.name, onError])

  if (!renderedComponent) {
    return (
      <div className="p-6 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="text-center text-gray-500">
          <div className="text-sm font-medium">Unable to render component</div>
          <div className="text-xs mt-1">Check the component code for errors</div>
        </div>
      </div>
    )
  }

  return renderedComponent
}

function applyPropertiesToJSX(jsx: string, properties: Record<string, any>): string {
  let processedJSX = jsx

  // Apply text content
  if (properties.title) {
    processedJSX = processedJSX.replace(
      /(\{[^}]*title[^}]*\}|>Your Title Here<|>.*?Title.*?<)/gi,
      `>${properties.title}<`
    )
  }

  if (properties.description) {
    processedJSX = processedJSX.replace(
      /(\{[^}]*description[^}]*\}|>Add your description here\.\.\.<|>.*?description.*?<)/gi,
      `>${properties.description}<`
    )
  }

  // Apply styling properties
  if (properties.backgroundColor) {
    processedJSX = applyBackgroundColor(processedJSX, properties.backgroundColor)
  }

  if (properties.textColor) {
    processedJSX = applyTextColor(processedJSX, properties.textColor)
  }

  if (properties.padding) {
    processedJSX = applySpacing(processedJSX, properties.padding, 'padding')
  }

  if (properties.margin) {
    processedJSX = applySpacing(processedJSX, properties.margin, 'margin')
  }

  if (properties.borderRadius) {
    processedJSX = applyBorderRadius(processedJSX, properties.borderRadius)
  }

  if (properties.shadow && properties.shadow !== 'none') {
    processedJSX = applyShadow(processedJSX, properties.shadow)
  }

  // Apply layout properties
  if (properties.width) {
    processedJSX = applyWidth(processedJSX, properties.width)
  }

  if (properties.height) {
    processedJSX = applyHeight(processedJSX, properties.height)
  }

  if (properties.alignment) {
    processedJSX = applyAlignment(processedJSX, properties.alignment)
  }

  // Apply content properties
  if (properties.titleSize) {
    processedJSX = applyTitleSize(processedJSX, properties.titleSize)
  }

  if (properties.image) {
    processedJSX = applyImage(processedJSX, properties.image, properties.imageAlt)
  }

  if (properties.icon) {
    processedJSX = applyIcon(processedJSX, properties.icon)
  }

  if (properties.links && Array.isArray(properties.links)) {
    processedJSX = applyLinks(processedJSX, properties.links)
  }

  // Apply behavior properties
  if (properties.animation && properties.animation !== 'none') {
    processedJSX = applyAnimation(processedJSX, properties.animation, properties.animationDuration)
  }

  if (properties.hoverEffect && properties.hoverEffect !== 'none') {
    processedJSX = applyHoverEffect(processedJSX, properties.hoverEffect)
  }

  return processedJSX
}

function applyBackgroundColor(jsx: string, color: string): string {
  // Convert hex color to Tailwind class or use custom CSS
  if (color.startsWith('#')) {
    return jsx.replace(
      /className="([^"]*?)"/g,
      `className="$1" style={{backgroundColor: '${color}'}}`
    )
  }
  return jsx
}

function applyTextColor(jsx: string, color: string): string {
  if (color.startsWith('#')) {
    return jsx.replace(
      /className="([^"]*?)"/g,
      `className="$1" style={{color: '${color}'}}`
    )
  }
  return jsx
}

function applySpacing(jsx: string, spacing: any, type: 'padding' | 'margin'): string {
  if (typeof spacing === 'object' && spacing !== null) {
    const { top, right, bottom, left } = spacing
    const cssProperty = type === 'padding' ? 'padding' : 'margin'
    const style = `${cssProperty}: ${top}px ${right}px ${bottom}px ${left}px`
    
    return jsx.replace(
      /className="([^"]*?)"/g,
      `className="$1" style={{${style}}}`
    )
  }
  return jsx
}

function applyBorderRadius(jsx: string, radius: number): string {
  return jsx.replace(
    /className="([^"]*?)"/g,
    `className="$1" style={{borderRadius: '${radius}px'}}`
  )
}

function applyShadow(jsx: string, shadow: string): string {
  return jsx.replace(
    /className="([^"]*?)"/g,
    `className="$1" style={{boxShadow: '${shadow}'}}`
  )
}

function applyWidth(jsx: string, width: string): string {
  return jsx.replace(
    /className="([^"]*?)w-\S+([^"]*?)"/g,
    `className="$1${width}$2"`
  )
}

function applyHeight(jsx: string, height: string): string {
  return jsx.replace(
    /className="([^"]*?)h-\S+([^"]*?)"/g,
    `className="$1${height}$2"`
  )
}

function applyAlignment(jsx: string, alignment: string): string {
  return jsx.replace(
    /className="([^"]*?)text-(left|center|right|justify)([^"]*?)"/g,
    `className="$1${alignment}$3"`
  )
}

function applyTitleSize(jsx: string, size: string): string {
  return jsx.replace(
    /className="([^"]*?)text-\w+([^"]*?)"/g,
    `className="$1${size}$2"`
  )
}

function applyImage(jsx: string, imageUrl: string, altText?: string): string {
  let result = jsx.replace(
    /src="[^"]*"/g,
    `src="${imageUrl}"`
  )
  
  if (altText) {
    result = result.replace(
      /alt="[^"]*"/g,
      `alt="${altText}"`
    )
  }
  
  return result
}

function applyIcon(jsx: string, iconName: string): string {
  // This would need to be implemented based on your icon system
  // For now, just replace icon references
  return jsx.replace(
    /icon="[^"]*"/g,
    `icon="${iconName}"`
  )
}

function applyLinks(jsx: string, links: Array<{text: string, url: string}>): string {
  // This would need more sophisticated parsing to handle repeater fields
  // For now, just a basic implementation
  return jsx
}

function applyAnimation(jsx: string, animation: string, duration?: number): string {
  const animationClass = getAnimationClass(animation)
  return jsx.replace(
    /className="([^"]*?)"/g,
    `className="$1 ${animationClass}"`
  )
}

function applyHoverEffect(jsx: string, effect: string): string {
  const hoverClass = getHoverEffectClass(effect)
  return jsx.replace(
    /className="([^"]*?)"/g,
    `className="$1 ${hoverClass}"`
  )
}

function getAnimationClass(animation: string): string {
  const animations: Record<string, string> = {
    fadeIn: 'animate-fade-in',
    slideUp: 'animate-slide-up',
    slideDown: 'animate-slide-down',
    scale: 'animate-scale',
    bounce: 'animate-bounce'
  }
  return animations[animation] || ''
}

function getHoverEffectClass(effect: string): string {
  const effects: Record<string, string> = {
    scale: 'hover:scale-105 transition-transform',
    lift: 'hover:-translate-y-1 hover:shadow-lg transition-all',
    glow: 'hover:shadow-lg hover:shadow-blue-500/25 transition-shadow',
    fade: 'hover:opacity-80 transition-opacity'
  }
  return effects[effect] || ''
}

function createSafeComponent(jsx: string, componentName: string) {
  try {
    // This is a simplified version - in a real implementation,
    // you'd want to use a proper JSX parser and renderer
    // For now, we'll create a basic HTML renderer
    
    return (
      <div 
        className="w-full"
        dangerouslySetInnerHTML={{ 
          __html: convertJSXToHTML(jsx) 
        }} 
      />
    )
  } catch (error) {
    console.error(`Error creating component ${componentName}:`, error)
    return null
  }
}

function convertJSXToHTML(jsx: string): string {
  // This is a very basic JSX to HTML converter
  // In a production app, you'd want to use a proper parser
  
  let html = jsx
  
  // Remove TypeScript interface definitions
  html = html.replace(/interface\s+\w+\s*{[^}]*}/gs, '')
  
  // Remove export statements
  html = html.replace(/export\s+(function|const)\s+/g, '')
  
  // Extract the return statement content
  const returnMatch = html.match(/return\s*\(([\s\S]*)\)/s)
  if (returnMatch) {
    html = returnMatch[1]
  }
  
  // Basic JSX to HTML conversions
  html = html.replace(/className=/g, 'class=')
  html = html.replace(/\{[^}]*\}/g, '') // Remove JSX expressions for now
  
  return html.trim()
}
