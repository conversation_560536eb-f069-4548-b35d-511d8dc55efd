// Main components
export { AIVisualEditor } from './components/ai-visual-editor'
export { AIVisualEditorLayout } from './components/ai-visual-editor-layout'
export { AiChatPanel } from './components/ai-chat-panel'
export { PropertiesPanel } from './components/properties-panel'
export { LivePreview } from './components/live-preview'
export { ComponentTree } from './components/component-tree'
export { DynamicComponentRenderer } from './components/dynamic-component-renderer'

// Store and hooks
export { useEditorStore, useSelectedComponent, useSelectedComponentProperties, useComponentById, useComponentProperties } from './stores/editor-store'

// Types
export type { 
  GeneratedComponent, 
  ComponentPropertiesConfig, 
  ComponentAnalysis, 
  AIToolResult, 
  EditorState, 
  EditorActions, 
  EditorStore,
  ComponentGenerationParams,
  PropertiesAnalysisParams,
  FieldGenerationResult
} from './types'

// Utilities
export { 
  analyzeComponentStructure, 
  extractPropsFromComponent, 
  generateComponentId, 
  validateComponentCode, 
  sanitizeComponentCode, 
  extractImportsFromComponent 
} from './utils/component-analyzer'

export { 
  generateAppearanceFields, 
  generateContentFields, 
  generateBehaviorFields, 
  generateDataFields, 
  generateLayoutFields, 
  generatePropertiesSchema, 
  generateDefaultValues 
} from './utils/properties-generator'
